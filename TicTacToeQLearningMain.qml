import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import TicTac<PERSON>oeGame 1.0

ApplicationWindow {
    id: root
    width: 650
    height: 800
    visible: true
    title: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Q-Learning AI"
    
    // Create the Q-learning game instance
    TicTacToeGame {
        id: game
    }

    // Main layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 15

        // Title
        Text {
            text: "🧠 Tic-<PERSON><PERSON><PERSON>To<PERSON> vs Q-Learning AI"
            font.pixelSize: 28
            font.bold: true
            color: "#2C3E50"
            Layout.alignment: Qt.AlignHCenter
        }

        // Training status
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: game.trainingMode ? "#E8F5E8" : "#F0F8FF"
            border.color: game.trainingMode ? "#4CAF50" : "#2196F3"
            border.width: 2
            radius: 8

            RowLayout {
                anchors.fill: parent
                anchors.margins: 10

                Text {
                    text: game.trainingMode ? "🎓 TRAINING MODE" : "🎮 PLAY MODE"
                    font.pixelSize: 14
                    font.bold: true
                    color: game.trainingMode ? "#2E7D32" : "#1565C0"
                }

                Text {
                    text: "Games: " + game.gamesPlayed
                    font.pixelSize: 12
                    color: "#666666"
                    Layout.fillWidth: true
                }

                Text {
                    text: "Exploration: " + (game.explorationRate * 100).toFixed(1) + "%"
                    font.pixelSize: 12
                    color: "#666666"
                }
            }
        }

        // Score board
        Rectangle {
            Layout.fillWidth: true
            height: 80
            color: "#ECF0F1"
            border.color: "#BDC3C7"
            border.width: 2
            radius: 10

            RowLayout {
                anchors.fill: parent
                anchors.margins: 15

                // Human score
                Rectangle {
                    Layout.fillWidth: true
                    height: 50
                    color: "#3498DB"
                    radius: 8

                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 2

                        Text {
                            text: "You (X)"
                            font.pixelSize: 14
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                        Text {
                            text: game.humanWins
                            font.pixelSize: 20
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }

                // Draws
                Rectangle {
                    Layout.fillWidth: true
                    height: 50
                    color: "#95A5A6"
                    radius: 8

                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 2

                        Text {
                            text: "Draws"
                            font.pixelSize: 14
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                        Text {
                            text: game.draws
                            font.pixelSize: 20
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }

                // AI score
                Rectangle {
                    Layout.fillWidth: true
                    height: 50
                    color: "#E74C3C"
                    radius: 8

                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 2

                        Text {
                            text: "AI (O)"
                            font.pixelSize: 14
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                        Text {
                            text: game.aiWins
                            font.pixelSize: 20
                            font.bold: true
                            color: "white"
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
            }
        }

        // Status message
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#F8F9FA"
            border.color: "#DEE2E6"
            border.width: 1
            radius: 8

            Text {
                anchors.centerIn: parent
                text: game.statusMessage
                font.pixelSize: 16
                font.bold: true
                color: "#495057"
                wrapMode: Text.WordWrap
                horizontalAlignment: Text.AlignHCenter
            }
        }

        // Game board
        Rectangle {
            Layout.alignment: Qt.AlignHCenter
            width: 360
            height: 360
            color: "#34495E"
            radius: 15
            border.color: "#2C3E50"
            border.width: 3

            Grid {
                id: gameGrid
                anchors.centerIn: parent
                rows: 3
                columns: 3
                spacing: 4

                Repeater {
                    model: 9
                    
                    Rectangle {
                        width: 116
                        height: 116
                        color: cellMouseArea.containsMouse ? "#5DADE2" : "#FFFFFF"
                        border.color: "#2C3E50"
                        border.width: 2
                        radius: 8

                        // Cell content
                        Text {
                            id: cellText
                            anchors.centerIn: parent
                            font.pixelSize: 48
                            font.bold: true
                            color: text === "X" ? "#3498DB" : "#E74C3C"
                            
                            function updateText() {
                                text = game.getCellValue(index)
                            }
                            
                            Component.onCompleted: updateText()
                            
                            Connections {
                                target: game
                                function onBoardChanged() {
                                    cellText.updateText()
                                }
                            }
                        }

                        // Mouse interaction
                        MouseArea {
                            id: cellMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            enabled: game.gameState === 0 && game.currentPlayer === 1 // Playing and human turn
                            
                            onClicked: {
                                if (game.isCellEmpty(index)) {
                                    if (game.makeHumanMove(index)) {
                                        // Delay AI move for better UX
                                        aiMoveTimer.start()
                                    }
                                }
                            }
                        }

                        // Hover indicator for empty cells
                        Text {
                            anchors.centerIn: parent
                            text: "X"
                            font.pixelSize: 48
                            font.bold: true
                            color: "#BDC3C7"
                            opacity: cellMouseArea.containsMouse && game.isCellEmpty(index) ? 0.5 : 0
                            visible: game.gameState === 0 && game.currentPlayer === 1
                        }
                    }
                }
            }
        }

        // Control buttons
        RowLayout {
            Layout.fillWidth: true
            spacing: 10

            Button {
                text: "New Game"
                font.pixelSize: 14
                font.bold: true
                Layout.fillWidth: true
                onClicked: game.resetGame()
            }

            Button {
                text: game.trainingMode ? "Disable Training" : "Enable Training"
                font.pixelSize: 14
                font.bold: true
                Layout.fillWidth: true
                onClicked: game.toggleTrainingMode()
            }

            Button {
                text: "Save AI"
                font.pixelSize: 14
                font.bold: true
                Layout.fillWidth: true
                onClicked: game.saveAIModel()
            }
        }

        // Second row of buttons
        RowLayout {
            Layout.fillWidth: true
            spacing: 10

            Button {
                text: "Reset Scores"
                font.pixelSize: 14
                font.bold: true
                Layout.fillWidth: true
                onClicked: game.resetScores()
            }
        }

        // Instructions
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: "#F8F9FA"
            border.color: "#DEE2E6"
            border.width: 1
            radius: 8

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10

                Text {
                    text: "Q-Learning Neural Network AI:"
                    font.pixelSize: 14
                    font.bold: true
                    color: "#495057"
                }

                Text {
                    text: "• The AI learns through Q-learning with a neural network\n• In training mode, it explores random moves to learn\n• As it plays more games, it gets smarter and explores less\n• Watch the exploration rate decrease as it learns!\n• You are X, AI is O - try to beat the learning AI! 🧠"
                    font.pixelSize: 11
                    color: "#6C757D"
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }
    }

    // Timer for AI move delay
    Timer {
        id: aiMoveTimer
        interval: game.trainingMode ? 200 : 800  // Faster in training mode
        repeat: false
        onTriggered: game.makeAIMove()
    }

    // Menu bar
    menuBar: MenuBar {
        Menu {
            title: "Game"
            MenuItem {
                text: "New Game"
                onTriggered: game.resetGame()
            }
            MenuItem {
                text: "Toggle Training Mode"
                onTriggered: game.toggleTrainingMode()
            }
            MenuItem {
                text: "Save AI Model"
                onTriggered: game.saveAIModel()
            }
            MenuSeparator {}
            MenuItem {
                text: "Reset Scores"
                onTriggered: game.resetScores()
            }
            MenuSeparator {}
            MenuItem {
                text: "Exit"
                onTriggered: Qt.quit()
            }
        }
        
        Menu {
            title: "Help"
            MenuItem {
                text: "About"
                onTriggered: aboutDialog.open()
            }
        }
    }

    // About dialog
    Dialog {
        id: aboutDialog
        title: "About Q-Learning Tic-Tac-Toe"
        anchors.centerIn: parent
        width: 400
        height: 300
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            
            Text {
                text: "🧠 Q-Learning Tic-Tac-Toe AI"
                font.pixelSize: 20
                font.bold: true
                Layout.alignment: Qt.AlignHCenter
            }
            
            Text {
                text: "Watch an AI learn to play Tic-Tac-Toe using reinforcement learning!"
                font.pixelSize: 14
                Layout.alignment: Qt.AlignHCenter
                wrapMode: Text.WordWrap
            }
            
            Text {
                text: "Features:\n• Deep Q-Network (DQN) with neural network\n• Experience replay for stable learning\n• Epsilon-greedy exploration strategy\n• Real-time learning visualization\n• Training mode with automatic improvement\n• Model saving and loading\n\nThe AI starts knowing nothing about Tic-Tac-Toe and learns purely through trial and error!"
                font.pixelSize: 12
                Layout.alignment: Qt.AlignHCenter
                Layout.fillHeight: true
                wrapMode: Text.WordWrap
            }
        }
        
        standardButtons: Dialog.Ok
    }
}
