#!/usr/bin/env python3
"""
Tic-Tac-Toe Game with Strong AI
Features minimax algorithm with alpha-beta pruning for unbeatable AI
"""

import sys
from enum import Enum
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApp<PERSON><PERSON>ngine, qmlRegisterType


class Player(Enum):
    EMPTY = 0
    HUMAN = 1  # X
    AI = 2     # O


class GameState(Enum):
    PLAYING = 0
    HUMAN_WIN = 1
    AI_WIN = 2
    DRAW = 3


class TicTacToeGame(QObject):
    # Signals for QML
    boardChanged = Signal()
    gameStateChanged = Signal()
    currentPlayerChanged = Signal()
    statusMessageChanged = Signal()
    scoresChanged = Signal()

    def __init__(self):
        super().__init__()
        self.board = [Player.EMPTY] * 9  # 3x3 board as flat array
        self._current_player = Player.HUMAN
        self._game_state = GameState.PLAYING
        self._status_message = "Your turn! Click a square to place X"
        self._human_wins = 0
        self._ai_wins = 0
        self._draws = 0
        self._board_update_counter = 0

    @Property(str, notify=statusMessageChanged)
    def statusMessage(self):
        return self._status_message

    @statusMessage.setter
    def statusMessage(self, message):
        if self._status_message != message:
            self._status_message = message
            self.statusMessageChanged.emit()

    @Property(int, notify=currentPlayerChanged)
    def currentPlayer(self):
        return self._current_player.value

    @Property(int, notify=gameStateChanged)
    def gameState(self):
        return self._game_state.value

    @Property(int, notify=scoresChanged)
    def humanWins(self):
        return self._human_wins

    @Property(int, notify=scoresChanged)
    def aiWins(self):
        return self._ai_wins

    @Property(int, notify=scoresChanged)
    def draws(self):
        return self._draws

    @Property(int, notify=boardChanged)
    def boardUpdateCounter(self):
        return self._board_update_counter

    @Slot(int, result=str)
    def getCellValue(self, index):
        """Get the symbol for a cell (X, O, or empty)"""
        if 0 <= index < 9:
            if self.board[index] == Player.HUMAN:
                return "X"
            elif self.board[index] == Player.AI:
                return "O"
        return ""

    @Slot(int, result=bool)
    def isCellEmpty(self, index):
        """Check if a cell is empty"""
        return 0 <= index < 9 and self.board[index] == Player.EMPTY

    @Slot(int, result=bool)
    def makeHumanMove(self, index):
        """Human player makes a move"""
        if (self._game_state != GameState.PLAYING or
            self._current_player != Player.HUMAN or
            not self.isCellEmpty(index)):
            return False

        # Make human move
        self.board[index] = Player.HUMAN
        self._board_update_counter += 1
        self.boardChanged.emit()

        # Check for game end
        if self._check_game_end():
            return True

        # Switch to AI turn
        self._current_player = Player.AI
        self.statusMessage = "AI is thinking..."
        self.currentPlayerChanged.emit()

        # AI makes move after a short delay (handled in QML)
        return True

    @Slot()
    def makeAIMove(self):
        """AI makes its move using minimax algorithm"""
        if self._game_state != GameState.PLAYING or self._current_player != Player.AI:
            return

        # Find best move using minimax
        best_move = self._get_best_move()

        if best_move != -1:
            self.board[best_move] = Player.AI
            self._board_update_counter += 1
            self.boardChanged.emit()

            # Check for game end
            if self._check_game_end():
                return

            # Switch back to human
            self._current_player = Player.HUMAN
            self.statusMessage = "Your turn! Click a square to place X"
            self.currentPlayerChanged.emit()

    def _get_best_move(self):
        """Find the best move using minimax with alpha-beta pruning"""
        best_score = float('-inf')
        best_move = -1

        for i in range(9):
            if self.board[i] == Player.EMPTY:
                # Try this move
                self.board[i] = Player.AI
                score = self._minimax(0, False, float('-inf'), float('inf'))
                self.board[i] = Player.EMPTY

                if score > best_score:
                    best_score = score
                    best_move = i

        return best_move

    def _minimax(self, depth, is_maximizing, alpha, beta):
        """Minimax algorithm with alpha-beta pruning"""
        # Check terminal states
        winner = self._get_winner()
        if winner == Player.AI:
            return 10 - depth  # Prefer quicker wins
        elif winner == Player.HUMAN:
            return depth - 10  # Prefer later losses
        elif self._is_board_full():
            return 0  # Draw

        if is_maximizing:
            # AI's turn (maximizing)
            max_score = float('-inf')
            for i in range(9):
                if self.board[i] == Player.EMPTY:
                    self.board[i] = Player.AI
                    score = self._minimax(depth + 1, False, alpha, beta)
                    self.board[i] = Player.EMPTY
                    max_score = max(score, max_score)
                    alpha = max(alpha, score)
                    if beta <= alpha:
                        break  # Alpha-beta pruning
            return max_score
        else:
            # Human's turn (minimizing)
            min_score = float('inf')
            for i in range(9):
                if self.board[i] == Player.EMPTY:
                    self.board[i] = Player.HUMAN
                    score = self._minimax(depth + 1, True, alpha, beta)
                    self.board[i] = Player.EMPTY
                    min_score = min(score, min_score)
                    beta = min(beta, score)
                    if beta <= alpha:
                        break  # Alpha-beta pruning
            return min_score

    def _get_winner(self):
        """Check if there's a winner"""
        # Winning combinations
        winning_combos = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # Columns
            [0, 4, 8], [2, 4, 6]              # Diagonals
        ]

        for combo in winning_combos:
            if (self.board[combo[0]] == self.board[combo[1]] == self.board[combo[2]]
                and self.board[combo[0]] != Player.EMPTY):
                return self.board[combo[0]]

        return Player.EMPTY

    def _is_board_full(self):
        """Check if the board is full"""
        return all(cell != Player.EMPTY for cell in self.board)

    def _check_game_end(self):
        """Check if the game has ended and update state"""
        winner = self._get_winner()

        if winner == Player.HUMAN:
            self._game_state = GameState.HUMAN_WIN
            self._human_wins += 1
            self.statusMessage = "🎉 You won! Great job!"
        elif winner == Player.AI:
            self._game_state = GameState.AI_WIN
            self._ai_wins += 1
            self.statusMessage = "🤖 AI wins! Better luck next time!"
        elif self._is_board_full():
            self._game_state = GameState.DRAW
            self._draws += 1
            self.statusMessage = "🤝 It's a draw! Well played!"
        else:
            return False

        self.gameStateChanged.emit()
        self.scoresChanged.emit()
        return True

    @Slot()
    def resetGame(self):
        """Reset the game for a new round"""
        self.board = [Player.EMPTY] * 9
        self._current_player = Player.HUMAN
        self._game_state = GameState.PLAYING
        self.statusMessage = "Your turn! Click a square to place X"
        self._board_update_counter += 1

        self.boardChanged.emit()
        self.currentPlayerChanged.emit()
        self.gameStateChanged.emit()

    @Slot()
    def resetScores(self):
        """Reset all scores"""
        self._human_wins = 0
        self._ai_wins = 0
        self._draws = 0
        self.scoresChanged.emit()


if __name__ == "__main__":
    app = QGuiApplication(sys.argv)

    # Register the TicTacToeGame type with QML
    qmlRegisterType(TicTacToeGame, "TicTacToeGame", 1, 0, "TicTacToeGame")

    engine = QQmlApplicationEngine()
    engine.load("TicTacToeMain.qml")

    if not engine.rootObjects():
        sys.exit(-1)

    sys.exit(app.exec())
