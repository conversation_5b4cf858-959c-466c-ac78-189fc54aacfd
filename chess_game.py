#!/usr/bin/env python3
"""
Chess Game Logic - Backend for Qt/QML Chess Game
Handles game state, move validation, and chess rules
"""

import sys
from enum import Enum
from typing import List, Tuple, Optional, Dict
from PySide6.QtCore import QObject, Signal, Slot, Property, QAbstractListModel, QModelIndex, Qt
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEng<PERSON>, qmlRegisterType


class PieceType(Enum):
    EMPTY = 0
    PAWN = 1
    ROOK = 2
    KNIGHT = 3
    BISHOP = 4
    QUEEN = 5
    KING = 6


class PieceColor(Enum):
    NONE = 0
    WHITE = 1
    BLACK = 2


class GameState(Enum):
    PLAYING = 0
    CHECK = 1
    CHECKMATE = 2
    STALEMATE = 3


class ChessPiece:
    def __init__(self, piece_type: PieceType = PieceType.EMPTY, color: PieceColor = PieceColor.NONE):
        self.type = piece_type
        self.color = color
        self.has_moved = False

    def __str__(self):
        if self.type == PieceType.EMPTY:
            return "  "

        symbols = {
            PieceType.PAWN: "♟♙",
            PieceType.ROOK: "♜♖",
            PieceType.KNIGHT: "♞♘",
            PieceType.BISHOP: "♝♗",
            PieceType.QUEEN: "♛♕",
            PieceType.KING: "♚♔"
        }

        symbol_pair = symbols[self.type]
        return symbol_pair[0] if self.color == PieceColor.BLACK else symbol_pair[1]


class ChessGame(QObject):
    # Signals for QML
    boardChanged = Signal()
    currentPlayerChanged = Signal()
    gameStateChanged = Signal()
    moveHistoryChanged = Signal()
    statusMessageChanged = Signal()

    def __init__(self):
        super().__init__()
        self.board = [[ChessPiece() for _ in range(8)] for _ in range(8)]
        self._current_player = PieceColor.WHITE
        self._game_state = GameState.PLAYING
        self._move_history = []
        self._status_message = "White to move"
        self.selected_square = None
        self.initialize_board()

    def initialize_board(self):
        """Set up the initial chess board position"""
        # Clear board
        for row in range(8):
            for col in range(8):
                self.board[row][col] = ChessPiece()

        # Place pawns
        for col in range(8):
            self.board[1][col] = ChessPiece(PieceType.PAWN, PieceColor.BLACK)
            self.board[6][col] = ChessPiece(PieceType.PAWN, PieceColor.WHITE)

        # Place other pieces
        piece_order = [PieceType.ROOK, PieceType.KNIGHT, PieceType.BISHOP, PieceType.QUEEN,
                      PieceType.KING, PieceType.BISHOP, PieceType.KNIGHT, PieceType.ROOK]

        for col, piece_type in enumerate(piece_order):
            self.board[0][col] = ChessPiece(piece_type, PieceColor.BLACK)
            self.board[7][col] = ChessPiece(piece_type, PieceColor.WHITE)

        self.boardChanged.emit()

    @Property(str, notify=statusMessageChanged)
    def statusMessage(self):
        return self._status_message

    @statusMessage.setter
    def statusMessage(self, message):
        if self._status_message != message:
            self._status_message = message
            self.statusMessageChanged.emit()

    @Property(int, notify=currentPlayerChanged)
    def currentPlayer(self):
        return self._current_player.value

    @Property(int, notify=gameStateChanged)
    def gameState(self):
        return self._game_state.value

    @Slot(int, int, result=str)
    def getPieceAt(self, row, col):
        """Get the piece symbol at the given position"""
        if 0 <= row < 8 and 0 <= col < 8:
            piece = self.board[row][col]
            if piece.type == PieceType.EMPTY:
                return ""
            return str(piece)
        return ""

    @Slot(int, int, result=int)
    def getPieceColorAt(self, row, col):
        """Get the piece color at the given position"""
        if 0 <= row < 8 and 0 <= col < 8:
            return self.board[row][col].color.value
        return 0

    @Slot(int, int, result=bool)
    def isValidSquare(self, row, col):
        """Check if the square coordinates are valid"""
        return 0 <= row < 8 and 0 <= col < 8

    def is_valid_move(self, from_row, from_col, to_row, to_col):
        """Check if a move is valid according to chess rules"""
        if not (self.isValidSquare(from_row, from_col) and self.isValidSquare(to_row, to_col)):
            return False

        piece = self.board[from_row][from_col]
        target = self.board[to_row][to_col]

        # Can't move empty square
        if piece.type == PieceType.EMPTY:
            return False

        # Can't move opponent's piece
        if piece.color != self._current_player:
            return False

        # Can't capture own piece
        if target.color == piece.color:
            return False

        # Can't move to same square
        if from_row == to_row and from_col == to_col:
            return False

        # Check piece-specific movement rules
        return self._is_valid_piece_move(piece, from_row, from_col, to_row, to_col)

    def _is_valid_piece_move(self, piece, from_row, from_col, to_row, to_col):
        """Check if the move is valid for the specific piece type"""
        row_diff = to_row - from_row
        col_diff = to_col - from_col

        if piece.type == PieceType.PAWN:
            return self._is_valid_pawn_move(piece, from_row, from_col, to_row, to_col)
        elif piece.type == PieceType.ROOK:
            return self._is_valid_rook_move(from_row, from_col, to_row, to_col)
        elif piece.type == PieceType.KNIGHT:
            return abs(row_diff) == 2 and abs(col_diff) == 1 or abs(row_diff) == 1 and abs(col_diff) == 2
        elif piece.type == PieceType.BISHOP:
            return self._is_valid_bishop_move(from_row, from_col, to_row, to_col)
        elif piece.type == PieceType.QUEEN:
            return (self._is_valid_rook_move(from_row, from_col, to_row, to_col) or
                   self._is_valid_bishop_move(from_row, from_col, to_row, to_col))
        elif piece.type == PieceType.KING:
            return abs(row_diff) <= 1 and abs(col_diff) <= 1

        return False

    def _is_valid_pawn_move(self, piece, from_row, from_col, to_row, to_col):
        """Validate pawn movement"""
        direction = -1 if piece.color == PieceColor.WHITE else 1
        row_diff = to_row - from_row
        col_diff = abs(to_col - from_col)

        target = self.board[to_row][to_col]

        # Forward move
        if col_diff == 0:
            if row_diff == direction and target.type == PieceType.EMPTY:
                return True
            # Initial two-square move
            if (not piece.has_moved and row_diff == 2 * direction and
                target.type == PieceType.EMPTY):
                return True
        # Diagonal capture
        elif col_diff == 1 and row_diff == direction and target.type != PieceType.EMPTY:
            return True

        return False

    def _is_valid_rook_move(self, from_row, from_col, to_row, to_col):
        """Validate rook movement (horizontal or vertical)"""
        if from_row != to_row and from_col != to_col:
            return False
        return self._is_path_clear(from_row, from_col, to_row, to_col)

    def _is_valid_bishop_move(self, from_row, from_col, to_row, to_col):
        """Validate bishop movement (diagonal)"""
        if abs(to_row - from_row) != abs(to_col - from_col):
            return False
        return self._is_path_clear(from_row, from_col, to_row, to_col)

    def _is_path_clear(self, from_row, from_col, to_row, to_col):
        """Check if path between two squares is clear"""
        row_step = 0 if from_row == to_row else (1 if to_row > from_row else -1)
        col_step = 0 if from_col == to_col else (1 if to_col > from_col else -1)

        current_row, current_col = from_row + row_step, from_col + col_step

        while current_row != to_row or current_col != to_col:
            if self.board[current_row][current_col].type != PieceType.EMPTY:
                return False
            current_row += row_step
            current_col += col_step

        return True

    @Slot(int, int, int, int, result=bool)
    def makeMove(self, from_row, from_col, to_row, to_col):
        """Attempt to make a move"""
        print(f"Attempting move from ({from_row},{from_col}) to ({to_row},{to_col})")

        if not self.is_valid_move(from_row, from_col, to_row, to_col):
            print(f"Invalid move from ({from_row},{from_col}) to ({to_row},{to_col})")
            return False

        # Make the move
        piece = self.board[from_row][from_col]
        captured_piece = self.board[to_row][to_col]

        print(f"Moving {piece} from ({from_row},{from_col}) to ({to_row},{to_col})")

        self.board[to_row][to_col] = piece
        self.board[from_row][from_col] = ChessPiece()
        piece.has_moved = True

        # Add to move history
        move_notation = f"{chr(ord('a') + from_col)}{8 - from_row}-{chr(ord('a') + to_col)}{8 - to_row}"
        self._move_history.append(move_notation)

        # Switch players
        self._current_player = PieceColor.BLACK if self._current_player == PieceColor.WHITE else PieceColor.WHITE

        # Update status
        player_name = "Black" if self._current_player == PieceColor.BLACK else "White"
        self.statusMessage = f"{player_name} to move"

        print(f"Move successful! Board updated. Current player: {player_name}")

        # Emit signals
        self.boardChanged.emit()
        self.currentPlayerChanged.emit()
        self.moveHistoryChanged.emit()

        return True

    @Slot()
    def resetGame(self):
        """Reset the game to initial state"""
        self._current_player = PieceColor.WHITE
        self._game_state = GameState.PLAYING
        self._move_history = []
        self.statusMessage = "White to move"
        self.selected_square = None
        self.initialize_board()
        self.currentPlayerChanged.emit()
        self.gameStateChanged.emit()
        self.moveHistoryChanged.emit()


if __name__ == "__main__":
    app = QGuiApplication(sys.argv)

    # Register the ChessGame type with QML
    qmlRegisterType(ChessGame, "ChessGame", 1, 0, "ChessGame")

    engine = QQmlApplicationEngine()
    engine.load("Main.qml")

    if not engine.rootObjects():
        sys.exit(-1)

    sys.exit(app.exec())
