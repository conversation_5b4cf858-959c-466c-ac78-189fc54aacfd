#!/usr/bin/env python3
"""
Chess Game Launcher
Runs the Qt/QML Chess Game
"""

import sys
import os
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine, qmlRegisterType

# Import our chess game logic
from chess_game import ChessG<PERSON>


def main():
    app = QGuiApplication(sys.argv)

    # Register the ChessGame type with QML
    qmlRegisterType(ChessGame, "ChessGame", 1, 0, "ChessGame")

    # Create QML engine
    engine = QQmlApplicationEngine()

    # Add the current directory and Example directory to the import path
    current_dir = Path(__file__).parent.absolute()
    example_dir = current_dir / "Example"
    engine.addImportPath(str(current_dir))
    engine.addImportPath(str(example_dir))

    # Load the main QML file
    main_qml = example_dir / "Main.qml"
    engine.load(str(main_qml))

    # Check if QML loaded successfully
    if not engine.rootObjects():
        print("Failed to load QML file")
        sys.exit(-1)

    print("Chess game started successfully!")
    print("Instructions:")
    print("- Click on a piece to select it")
    print("- Click on a destination square to move")
    print("- Use the 'New Game' button to restart")

    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
