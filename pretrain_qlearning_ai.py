#!/usr/bin/env python3
"""
Pre-training script for Q-Learning Tic-Tac-Toe AI
Trains the AI through self-play for thousands of episodes
"""

import numpy as np
import random
import time
from tqdm import tqdm
from tictactoe_qlearning import Player, QLearningAgent


class TicTacToeEnvironment:
    """Tic-Tac-Toe environment for self-play training"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset the game to initial state"""
        self.board = [Player.EMPTY] * 9
        self.current_player = Player.HUMAN  # Start with player 1 (X)
        self.game_over = False
        self.winner = Player.EMPTY
        return self.get_state()
    
    def get_state(self):
        """Get current board state as numpy array"""
        state = np.zeros(9)
        for i, cell in enumerate(self.board):
            if cell == Player.HUMAN:
                state[i] = -1  # Player 1 (X) = -1
            elif cell == Player.AI:
                state[i] = 1   # Player 2 (O) = 1
            # Empty = 0
        return state
    
    def get_valid_actions(self):
        """Get list of valid moves"""
        return [i for i, cell in enumerate(self.board) if cell == Player.EMPTY]
    
    def make_move(self, action):
        """Make a move and return (next_state, reward, done, info)"""
        if action not in self.get_valid_actions():
            # Invalid move - heavily penalize
            return self.get_state(), -100, True, {"invalid_move": True}
        
        # Make the move
        self.board[action] = self.current_player
        
        # Check for game end
        winner = self._check_winner()
        reward = 0
        
        if winner != Player.EMPTY:
            self.game_over = True
            self.winner = winner
            if winner == self.current_player:
                reward = 10  # Win
            else:
                reward = -10  # Lose (shouldn't happen in this turn)
        elif self._is_board_full():
            self.game_over = True
            self.winner = Player.EMPTY
            reward = 1  # Draw
        else:
            reward = 0.1  # Small positive reward for valid move
        
        # Switch players
        self.current_player = Player.AI if self.current_player == Player.HUMAN else Player.HUMAN
        
        return self.get_state(), reward, self.game_over, {"winner": self.winner}
    
    def _check_winner(self):
        """Check if there's a winner"""
        winning_combos = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # Columns
            [0, 4, 8], [2, 4, 6]              # Diagonals
        ]
        
        for combo in winning_combos:
            if (self.board[combo[0]] == self.board[combo[1]] == self.board[combo[2]] 
                and self.board[combo[0]] != Player.EMPTY):
                return self.board[combo[0]]
        
        return Player.EMPTY
    
    def _is_board_full(self):
        """Check if board is full"""
        return all(cell != Player.EMPTY for cell in self.board)


def self_play_episode(agent1, agent2, env):
    """Play one episode of self-play between two agents"""
    state = env.reset()
    episode_data = []
    
    while not env.game_over:
        current_agent = agent1 if env.current_player == Player.HUMAN else agent2
        
        # Choose action
        valid_actions = env.get_valid_actions()
        if not valid_actions:
            break
            
        action = current_agent.choose_action(env.board)
        
        # Store state-action pair
        episode_data.append({
            'player': env.current_player,
            'state': state.copy(),
            'action': action,
            'agent': current_agent
        })
        
        # Make move
        next_state, reward, done, info = env.make_move(action)
        state = next_state
    
    # Assign rewards based on final outcome
    final_reward_p1 = 0
    final_reward_p2 = 0
    
    if env.winner == Player.HUMAN:
        final_reward_p1 = 10
        final_reward_p2 = -10
    elif env.winner == Player.AI:
        final_reward_p1 = -10
        final_reward_p2 = 10
    else:  # Draw
        final_reward_p1 = 1
        final_reward_p2 = 1
    
    # Store experiences in replay memory
    for i, data in enumerate(episode_data):
        player = data['player']
        agent = data['agent']
        state = data['state']
        action = data['action']
        
        # Get next state (or final state)
        if i < len(episode_data) - 1:
            next_state = episode_data[i + 1]['state']
            done = False
        else:
            next_state = env.get_state()
            done = True
        
        # Assign reward
        if player == Player.HUMAN:
            reward = final_reward_p1 if done else 0.1
        else:
            reward = final_reward_p2 if done else 0.1
        
        # Store experience
        agent.remember(state, action, reward, next_state, done)
    
    return env.winner


def pretrain_ai(episodes=10000, save_interval=1000, model_path="tictactoe_qlearning_model.pkl"):
    """Pre-train the AI using self-play"""
    
    print("🧠 Starting Q-Learning AI Pre-training")
    print("="*50)
    print(f"Episodes: {episodes}")
    print(f"Save interval: {save_interval}")
    print(f"Model path: {model_path}")
    print("="*50)
    
    # Create environment and agents
    env = TicTacToeEnvironment()
    
    # Create two agents (they will share experiences)
    agent1 = QLearningAgent(
        learning_rate=0.001,
        epsilon=0.9,  # High exploration initially
        epsilon_decay=0.9995,
        epsilon_min=0.05,
        gamma=0.95
    )
    
    # Agent 2 shares the same network but can have different exploration
    agent2 = agent1  # Same agent playing both sides
    
    # Training statistics
    wins_p1 = 0
    wins_p2 = 0
    draws = 0
    
    # Training loop
    start_time = time.time()
    
    for episode in tqdm(range(episodes), desc="Training"):
        # Play one episode
        winner = self_play_episode(agent1, agent2, env)
        
        # Update statistics
        if winner == Player.HUMAN:
            wins_p1 += 1
        elif winner == Player.AI:
            wins_p2 += 1
        else:
            draws += 1
        
        # Train the agent
        if len(agent1.memory) > 32:
            agent1.train(batch_size=32)
        
        # Update target network periodically
        if episode % 100 == 0 and hasattr(agent1, 'update_target_network'):
            agent1.update_target_network()
        
        # Save model periodically
        if (episode + 1) % save_interval == 0:
            agent1.save_model(model_path)
            elapsed_time = time.time() - start_time
            
            print(f"\n📊 Episode {episode + 1}/{episodes}")
            print(f"⏱️  Time elapsed: {elapsed_time:.1f}s")
            print(f"🎯 Epsilon: {agent1.epsilon:.4f}")
            print(f"📈 Win rate P1: {wins_p1/(episode+1)*100:.1f}%")
            print(f"📈 Win rate P2: {wins_p2/(episode+1)*100:.1f}%")
            print(f"📈 Draw rate: {draws/(episode+1)*100:.1f}%")
            print(f"💾 Model saved to {model_path}")
            print("-" * 40)
    
    # Final save
    agent1.save_model(model_path)
    
    # Final statistics
    total_time = time.time() - start_time
    print("\n🎉 Pre-training Complete!")
    print("="*50)
    print(f"📊 Final Statistics:")
    print(f"⏱️  Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
    print(f"🎯 Final epsilon: {agent1.epsilon:.4f}")
    print(f"📈 Player 1 wins: {wins_p1} ({wins_p1/episodes*100:.1f}%)")
    print(f"📈 Player 2 wins: {wins_p2} ({wins_p2/episodes*100:.1f}%)")
    print(f"📈 Draws: {draws} ({draws/episodes*100:.1f}%)")
    print(f"🧠 Memory size: {len(agent1.memory)}")
    print(f"💾 Model saved to: {model_path}")
    print("="*50)
    
    # Set to evaluation mode
    agent1.set_training_mode(False)
    
    return agent1


def test_pretrained_ai(model_path="tictactoe_qlearning_model.pkl", test_games=100):
    """Test the pre-trained AI"""
    print(f"\n🧪 Testing pre-trained AI with {test_games} games...")
    
    # Load the trained agent
    agent = QLearningAgent()
    agent.load_model(model_path)
    agent.set_training_mode(False)  # Disable exploration
    
    env = TicTacToeEnvironment()
    
    # Test against random player
    wins = 0
    draws = 0
    losses = 0
    
    for _ in tqdm(range(test_games), desc="Testing"):
        state = env.reset()
        
        while not env.game_over:
            if env.current_player == Player.HUMAN:
                # AI plays as Player 1
                action = agent.choose_action(env.board)
            else:
                # Random player
                valid_actions = env.get_valid_actions()
                action = random.choice(valid_actions) if valid_actions else 0
            
            state, reward, done, info = env.make_move(action)
        
        # Count results (from AI's perspective as Player 1)
        if env.winner == Player.HUMAN:
            wins += 1
        elif env.winner == Player.AI:
            losses += 1
        else:
            draws += 1
    
    print(f"🎯 Test Results vs Random Player:")
    print(f"   Wins: {wins}/{test_games} ({wins/test_games*100:.1f}%)")
    print(f"   Draws: {draws}/{test_games} ({draws/test_games*100:.1f}%)")
    print(f"   Losses: {losses}/{test_games} ({losses/test_games*100:.1f}%)")


if __name__ == "__main__":
    # Pre-train the AI
    trained_agent = pretrain_ai(
        episodes=20000,  # Train for 20,000 episodes
        save_interval=2000,
        model_path="tictactoe_qlearning_model.pkl"
    )
    
    # Test the trained AI
    test_pretrained_ai("tictactoe_qlearning_model.pkl", test_games=1000)
    
    print("\n🎮 Pre-training complete! You can now run the game with:")
    print("python run_tictactoe_qlearning.py")
    print("\nThe AI will load the pre-trained model and be much stronger! 💪")
