#!/usr/bin/env python3
"""
Tic-Tac-Toe Game with Q-Learning Neural Network AI
Features a neural network trained with Q-learning instead of minimax
"""

import sys
import numpy as np
from enum import Enum
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine, qmlRegisterType

from tictactoe_qlearning import Player, GameState, QLearningAgent


class TicTacToeQLearningGame(QObject):
    # Signals for QML
    boardChanged = Signal()
    gameStateChanged = Signal()
    currentPlayerChanged = Signal()
    statusMessageChanged = Signal()
    scoresChanged = Signal()
    trainingStatusChanged = Signal()

    def __init__(self):
        super().__init__()
        self.board = [Player.EMPTY] * 9  # 3x3 board as flat array
        self._current_player = Player.HUMAN
        self._game_state = GameState.PLAYING
        self._status_message = "Your turn! Click a square to place X"
        self._human_wins = 0
        self._ai_wins = 0
        self._draws = 0
        self._board_update_counter = 0
        
        # Initialize Q-learning agent
        self.ai_agent = QLearningAgent()
        self._training_mode = True
        self._games_played = 0
        self._training_games = 1000  # Number of games for training
        
        # Store previous state and action for learning
        self.previous_state = None
        self.previous_action = None
        
        print(f"Q-Learning AI initialized!")
        print(f"Training mode: {self._training_mode}")
        print(f"Current epsilon (exploration): {self.ai_agent.epsilon:.3f}")

    @Property(str, notify=statusMessageChanged)
    def statusMessage(self):
        return self._status_message

    @statusMessage.setter
    def statusMessage(self, message):
        if self._status_message != message:
            self._status_message = message
            self.statusMessageChanged.emit()

    @Property(int, notify=currentPlayerChanged)
    def currentPlayer(self):
        return self._current_player.value

    @Property(int, notify=gameStateChanged)
    def gameState(self):
        return self._game_state.value

    @Property(int, notify=scoresChanged)
    def humanWins(self):
        return self._human_wins

    @Property(int, notify=scoresChanged)
    def aiWins(self):
        return self._ai_wins

    @Property(int, notify=scoresChanged)
    def draws(self):
        return self._draws

    @Property(int, notify=boardChanged)
    def boardUpdateCounter(self):
        return self._board_update_counter

    @Property(bool, notify=trainingStatusChanged)
    def trainingMode(self):
        return self._training_mode

    @Property(int, notify=trainingStatusChanged)
    def gamesPlayed(self):
        return self._games_played

    @Property(float, notify=trainingStatusChanged)
    def explorationRate(self):
        return self.ai_agent.epsilon

    @Slot(int, result=str)
    def getCellValue(self, index):
        """Get the symbol for a cell (X, O, or empty)"""
        if 0 <= index < 9:
            if self.board[index] == Player.HUMAN:
                return "X"
            elif self.board[index] == Player.AI:
                return "O"
        return ""

    @Slot(int, result=bool)
    def isCellEmpty(self, index):
        """Check if a cell is empty"""
        return 0 <= index < 9 and self.board[index] == Player.EMPTY

    @Slot(int, result=bool)
    def makeHumanMove(self, index):
        """Human player makes a move"""
        if (self._game_state != GameState.PLAYING or 
            self._current_player != Player.HUMAN or 
            not self.isCellEmpty(index)):
            return False

        # Make human move
        self.board[index] = Player.HUMAN
        self._board_update_counter += 1
        self.boardChanged.emit()

        # Check for game end
        if self._check_game_end():
            self._handle_game_end()
            return True

        # Switch to AI turn
        self._current_player = Player.AI
        self.statusMessage = "AI is thinking..."
        self.currentPlayerChanged.emit()

        return True

    @Slot()
    def makeAIMove(self):
        """AI makes its move using Q-learning neural network"""
        if self._game_state != GameState.PLAYING or self._current_player != Player.AI:
            return

        # Get current state for the AI
        current_state = self.ai_agent.get_state_representation(self.board)
        
        # Choose action using Q-learning policy
        action = self.ai_agent.choose_action(self.board)
        
        if action != -1 and self.isCellEmpty(action):
            # Store experience if we have a previous state
            if self.previous_state is not None and self.previous_action is not None:
                reward = self._calculate_reward(Player.AI)
                self.ai_agent.remember(
                    self.previous_state, 
                    self.previous_action, 
                    reward, 
                    current_state, 
                    False
                )
            
            # Make the move
            self.board[action] = Player.AI
            self._board_update_counter += 1
            self.boardChanged.emit()
            
            # Store current state and action for next learning step
            self.previous_state = current_state.copy()
            self.previous_action = action

            # Check for game end
            if self._check_game_end():
                self._handle_game_end()
                return

            # Switch back to human
            self._current_player = Player.HUMAN
            self.statusMessage = "Your turn! Click a square to place X"
            self.currentPlayerChanged.emit()

    def _calculate_reward(self, player):
        """Calculate reward for the AI based on game state"""
        winner = self._get_winner()
        
        if winner == Player.AI:
            return 10  # AI wins
        elif winner == Player.HUMAN:
            return -10  # AI loses
        elif self._is_board_full():
            return 1   # Draw (slightly positive)
        else:
            # Game continues - small reward for not losing immediately
            return 0.1

    def _handle_game_end(self):
        """Handle end of game for Q-learning"""
        # Final reward for the AI
        if self.previous_state is not None and self.previous_action is not None:
            final_reward = self._calculate_reward(Player.AI)
            current_state = self.ai_agent.get_state_representation(self.board)
            
            self.ai_agent.remember(
                self.previous_state,
                self.previous_action,
                final_reward,
                current_state,
                True  # Game is done
            )
        
        # Train the AI if in training mode
        if self._training_mode:
            self.ai_agent.train()
            
            # Update target network periodically
            if self._games_played % 100 == 0:
                if hasattr(self.ai_agent, 'update_target_network'):
                    self.ai_agent.update_target_network()
        
        # Reset for next game
        self.previous_state = None
        self.previous_action = None
        
        # Increment games played
        self._games_played += 1
        
        # Check if we should disable training mode
        if self._training_mode and self._games_played >= self._training_games:
            self._training_mode = False
            self.ai_agent.set_training_mode(False)
            self.ai_agent.save_model()
            self.statusMessage = f"🎓 AI training complete! Played {self._games_played} games."
            print(f"Training completed after {self._games_played} games")
            print(f"Final exploration rate: {self.ai_agent.epsilon:.3f}")
        
        self.trainingStatusChanged.emit()

    def _get_winner(self):
        """Check if there's a winner"""
        # Winning combinations
        winning_combos = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # Columns
            [0, 4, 8], [2, 4, 6]              # Diagonals
        ]

        for combo in winning_combos:
            if (self.board[combo[0]] == self.board[combo[1]] == self.board[combo[2]] 
                and self.board[combo[0]] != Player.EMPTY):
                return self.board[combo[0]]

        return Player.EMPTY

    def _is_board_full(self):
        """Check if the board is full"""
        return all(cell != Player.EMPTY for cell in self.board)

    def _check_game_end(self):
        """Check if the game has ended and update state"""
        winner = self._get_winner()
        
        if winner == Player.HUMAN:
            self._game_state = GameState.HUMAN_WIN
            self._human_wins += 1
            if self._training_mode:
                self.statusMessage = f"🎉 You won! (Training game {self._games_played + 1}/{self._training_games})"
            else:
                self.statusMessage = "🎉 You won! Great job!"
        elif winner == Player.AI:
            self._game_state = GameState.AI_WIN
            self._ai_wins += 1
            if self._training_mode:
                self.statusMessage = f"🤖 AI wins! (Training game {self._games_played + 1}/{self._training_games})"
            else:
                self.statusMessage = "🤖 AI wins! The neural network is learning!"
        elif self._is_board_full():
            self._game_state = GameState.DRAW
            self._draws += 1
            if self._training_mode:
                self.statusMessage = f"🤝 Draw! (Training game {self._games_played + 1}/{self._training_games})"
            else:
                self.statusMessage = "🤝 It's a draw! Well played!"
        else:
            return False

        self.gameStateChanged.emit()
        self.scoresChanged.emit()
        return True

    @Slot()
    def resetGame(self):
        """Reset the game for a new round"""
        self.board = [Player.EMPTY] * 9
        self._current_player = Player.HUMAN
        self._game_state = GameState.PLAYING
        
        if self._training_mode:
            self.statusMessage = f"Training game {self._games_played + 1}/{self._training_games} - Your turn!"
        else:
            self.statusMessage = "Your turn! Click a square to place X"
        
        self._board_update_counter += 1
        
        # Reset learning state
        self.previous_state = None
        self.previous_action = None
        
        self.boardChanged.emit()
        self.currentPlayerChanged.emit()
        self.gameStateChanged.emit()

    @Slot()
    def resetScores(self):
        """Reset all scores"""
        self._human_wins = 0
        self._ai_wins = 0
        self._draws = 0
        self.scoresChanged.emit()

    @Slot()
    def toggleTrainingMode(self):
        """Toggle training mode on/off"""
        self._training_mode = not self._training_mode
        self.ai_agent.set_training_mode(self._training_mode)
        
        if self._training_mode:
            self.statusMessage = "🎓 Training mode enabled - AI will learn from games"
        else:
            self.statusMessage = "🎮 Training mode disabled - AI using learned strategy"
            self.ai_agent.save_model()
        
        self.trainingStatusChanged.emit()

    @Slot()
    def saveAIModel(self):
        """Manually save the AI model"""
        self.ai_agent.save_model()
        self.statusMessage = "💾 AI model saved successfully!"


if __name__ == "__main__":
    app = QGuiApplication(sys.argv)
    
    # Register the TicTacToeQLearningGame type with QML
    qmlRegisterType(TicTacToeQLearningGame, "TicTacToeGame", 1, 0, "TicTacToeGame")
    
    engine = QQmlApplicationEngine()
    engine.load("TicTacToeQLearningMain.qml")
    
    if not engine.rootObjects():
        sys.exit(-1)
    
    sys.exit(app.exec())
