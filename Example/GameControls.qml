import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import ChessGame 1.0

Rectangle {
    id: gameControls
    width: 300
    height: 480
    color: "#F5F5DC"
    border.color: "#8B4513"
    border.width: 2

    property ChessGame game: null

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 15

        // Game title
        Text {
            text: "♔ Chess Game ♛"
            font.pixelSize: 24
            font.bold: true
            color: "#8B4513"
            Layout.alignment: Qt.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: "#8B4513"
        }

        // Game status
        Rectangle {
            Layout.fillWidth: true
            height: 80
            color: "#FFFFFF"
            border.color: "#8B4513"
            border.width: 1
            radius: 5

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10

                Text {
                    text: "Game Status"
                    font.pixelSize: 14
                    font.bold: true
                    color: "#8B4513"
                }

                Text {
                    id: statusText
                    text: gameControls.game ? gameControls.game.statusMessage : "No game loaded"
                    font.pixelSize: 12
                    color: "#333333"
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }

        // Current player indicator
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#FFFFFF"
            border.color: "#8B4513"
            border.width: 1
            radius: 5

            RowLayout {
                anchors.fill: parent
                anchors.margins: 10

                Text {
                    text: "Current Player:"
                    font.pixelSize: 14
                    font.bold: true
                    color: "#8B4513"
                }

                Rectangle {
                    width: 30
                    height: 30
                    radius: 15
                    color: {
                        if (!gameControls.game) return "#CCCCCC"
                        return gameControls.game.currentPlayer === 1 ? "#FFFFFF" : "#333333"
                    }
                    border.color: "#8B4513"
                    border.width: 2

                    Text {
                        anchors.centerIn: parent
                        text: {
                            if (!gameControls.game) return ""
                            return gameControls.game.currentPlayer === 1 ? "♔" : "♚"
                        }
                        font.pixelSize: 16
                        color: {
                            if (!gameControls.game) return "#666666"
                            return gameControls.game.currentPlayer === 1 ? "#333333" : "#FFFFFF"
                        }
                    }
                }

                Text {
                    text: {
                        if (!gameControls.game) return "None"
                        return gameControls.game.currentPlayer === 1 ? "White" : "Black"
                    }
                    font.pixelSize: 12
                    color: "#333333"
                }
            }
        }

        // Game controls
        GroupBox {
            title: "Game Controls"
            Layout.fillWidth: true
            font.pixelSize: 14
            font.bold: true

            ColumnLayout {
                anchors.fill: parent
                spacing: 10

                Button {
                    text: "New Game"
                    Layout.fillWidth: true
                    font.pixelSize: 12
                    
                    background: Rectangle {
                        color: parent.pressed ? "#D2691E" : (parent.hovered ? "#DEB887" : "#F4A460")
                        border.color: "#8B4513"
                        border.width: 1
                        radius: 4
                    }

                    onClicked: {
                        if (gameControls.game) {
                            gameControls.game.resetGame()
                        }
                    }
                }

                Button {
                    text: "Undo Move"
                    Layout.fillWidth: true
                    font.pixelSize: 12
                    enabled: false  // TODO: Implement undo functionality
                    
                    background: Rectangle {
                        color: parent.enabled ? 
                               (parent.pressed ? "#D2691E" : (parent.hovered ? "#DEB887" : "#F4A460")) :
                               "#CCCCCC"
                        border.color: "#8B4513"
                        border.width: 1
                        radius: 4
                    }

                    onClicked: {
                        // TODO: Implement undo functionality
                    }
                }
            }
        }

        // Move history
        GroupBox {
            title: "Move History"
            Layout.fillWidth: true
            Layout.fillHeight: true
            font.pixelSize: 14
            font.bold: true

            ScrollView {
                anchors.fill: parent
                clip: true

                ListView {
                    id: moveHistoryList
                    model: ListModel {
                        id: moveHistoryModel
                    }

                    delegate: Rectangle {
                        width: moveHistoryList.width
                        height: 25
                        color: index % 2 === 0 ? "#FFFFFF" : "#F0F0F0"

                        Text {
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            text: (Math.floor(index / 2) + 1) + ". " + model.move
                            font.pixelSize: 10
                            color: "#333333"
                        }
                    }
                }
            }
        }

        // Game statistics
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#FFFFFF"
            border.color: "#8B4513"
            border.width: 1
            radius: 5

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10

                Text {
                    text: "Game Statistics"
                    font.pixelSize: 12
                    font.bold: true
                    color: "#8B4513"
                }

                Text {
                    text: "Moves: " + (moveHistoryModel.count)
                    font.pixelSize: 10
                    color: "#333333"
                }
            }
        }
    }

    // Connect to game signals to update move history
    Connections {
        target: gameControls.game
        function onMoveHistoryChanged() {
            // This would need to be implemented in the Python backend
            // For now, we'll just show a placeholder
        }
    }

    // Update status text when game status changes
    Connections {
        target: gameControls.game
        function onStatusMessageChanged() {
            // The binding should handle this automatically
        }
    }
}
