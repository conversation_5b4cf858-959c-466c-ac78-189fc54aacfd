import QtQuick
import QtQuick.Controls
import ChessGame 1.0

Rectangle {
    id: chessBoard
    width: 480
    height: 480
    color: "#8B4513"
    border.color: "#654321"
    border.width: 4

    property ChessGame game: null
    property int selectedRow: -1
    property int selectedCol: -1
    property var validMoves: []

    // Chess board grid
    Grid {
        id: boardGrid
        anchors.centerIn: parent
        rows: 8
        columns: 8
        spacing: 0

        Repeater {
            model: 64

            Rectangle {
                id: square
                width: 56
                height: 56

                property int row: Math.floor(index / 8)
                property int col: index % 8
                property bool isLight: (row + col) % 2 === 0
                property bool isSelected: chessBoard.selectedRow === row && chessBoard.selectedCol === col
                property bool isValidMove: chessBoard.validMoves.some(move => move.row === row && move.col === col)

                color: {
                    if (isSelected) return "#FFD700"  // Gold for selected
                    if (isValidMove) return "#90EE90"  // Light green for valid moves
                    return isLight ? "#F0D9B5" : "#B58863"  // Standard chess colors
                }

                border.color: isSelected ? "#FF8C00" : "transparent"
                border.width: isSelected ? 3 : 0

                // Chess piece display
                Text {
                    id: pieceText
                    anchors.centerIn: parent
                    font.pixelSize: 36
                    font.family: "Arial Unicode MS, Segoe UI Symbol"
                    text: chessBoard.game ? chessBoard.game.getPieceAt(square.row, square.col) : ""
                    color: {
                        if (!chessBoard.game) return "black"
                        let pieceColor = chessBoard.game.getPieceColorAt(square.row, square.col)
                        return pieceColor === 1 ? "#FFFFFF" : "#000000"  // 1 = White, 2 = Black
                    }

                    // Add text shadow for better visibility
                    style: Text.Outline
                    styleColor: color === "#FFFFFF" ? "#000000" : "#FFFFFF"
                }

                // Square coordinates (for debugging/reference)
                Text {
                    anchors.bottom: parent.bottom
                    anchors.right: parent.right
                    anchors.margins: 2
                    font.pixelSize: 8
                    color: square.isLight ? "#8B4513" : "#F0D9B5"
                    text: String.fromCharCode(97 + square.col) + (8 - square.row)
                    visible: false  // Set to true for debugging
                }

                MouseArea {
                    id: mouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: {
                        if (!chessBoard.game) return

                        // If no square is selected, select this square (if it has a piece of current player)
                        if (chessBoard.selectedRow === -1) {
                            let pieceColor = chessBoard.game.getPieceColorAt(square.row, square.col)
                            let currentPlayer = chessBoard.game.currentPlayer

                            if (pieceColor === currentPlayer) {
                                chessBoard.selectedRow = square.row
                                chessBoard.selectedCol = square.col
                                chessBoard.updateValidMoves()
                            }
                        }
                        // If a square is already selected
                        else {
                            // If clicking the same square, deselect
                            if (chessBoard.selectedRow === square.row && chessBoard.selectedCol === square.col) {
                                chessBoard.clearSelection()
                            }
                            // If clicking a different square, try to make a move
                            else {
                                let moveSuccessful = chessBoard.game.makeMove(
                                    chessBoard.selectedRow, chessBoard.selectedCol,
                                    square.row, square.col
                                )

                                if (moveSuccessful) {
                                    chessBoard.clearSelection()
                                } else {
                                    // If move failed, check if clicking on own piece to select it
                                    let pieceColor = chessBoard.game.getPieceColorAt(square.row, square.col)
                                    let currentPlayer = chessBoard.game.currentPlayer

                                    if (pieceColor === currentPlayer) {
                                        chessBoard.selectedRow = square.row
                                        chessBoard.selectedCol = square.col
                                        chessBoard.updateValidMoves()
                                    } else {
                                        chessBoard.clearSelection()
                                    }
                                }
                            }
                        }
                    }
                }

                // Hover effect
                Rectangle {
                    anchors.fill: parent
                    color: "transparent"
                    border.color: "#FFD700"
                    border.width: 1
                    visible: mouseArea.containsMouse && !square.isSelected
                    opacity: 0.5
                }
            }
        }
    }

    // Board labels (files and ranks)
    // File labels (a-h)
    Row {
        anchors.horizontalCenter: boardGrid.horizontalCenter
        anchors.top: boardGrid.bottom
        anchors.topMargin: 5
        spacing: 56

        Repeater {
            model: ["a", "b", "c", "d", "e", "f", "g", "h"]
            Text {
                text: modelData
                font.pixelSize: 14
                font.bold: true
                color: "#654321"
                width: 56
                horizontalAlignment: Text.AlignHCenter
            }
        }
    }

    // Rank labels (1-8)
    Column {
        anchors.verticalCenter: boardGrid.verticalCenter
        anchors.right: boardGrid.left
        anchors.rightMargin: 5
        spacing: 56

        Repeater {
            model: ["8", "7", "6", "5", "4", "3", "2", "1"]
            Text {
                text: modelData
                font.pixelSize: 14
                font.bold: true
                color: "#654321"
                height: 56
                verticalAlignment: Text.AlignVCenter
            }
        }
    }

    function clearSelection() {
        selectedRow = -1
        selectedCol = -1
        validMoves = []
    }

    function updateValidMoves() {
        // For now, we'll implement a simple version
        // In a full implementation, this would calculate all valid moves for the selected piece
        validMoves = []

        if (selectedRow === -1 || selectedCol === -1 || !game) return

        // This is a simplified version - in a real game you'd want to calculate actual valid moves
        // For now, we'll just highlight potential target squares
        let moves = []
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (row !== selectedRow || col !== selectedCol) {
                    moves.push({row: row, col: col})
                }
            }
        }
        validMoves = moves
    }

    // Connect to game signals
    Connections {
        target: chessBoard.game
        function onBoardChanged() {
            // Force update of all pieces by triggering a refresh
            // The Repeater will automatically update when the game state changes
        }
    }
}
