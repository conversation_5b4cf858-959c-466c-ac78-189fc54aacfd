import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import ChessGame 1.0

ApplicationWindow {
    id: root
    width: 820
    height: 520
    visible: true
    title: "Qt Chess Game"

    // Create the chess game instance
    ChessGame {
        id: chessGame
    }

    // Main layout
    RowLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 15

        // Chess board
        ChessBoard {
            id: chessBoard
            game: chessGame
            Layout.alignment: Qt.AlignCenter
        }

        // Game controls and info
        GameControls {
            id: gameControls
            game: chessGame
            Layout.alignment: Qt.AlignTop
        }
    }

    // Menu bar
    menuBar: MenuBar {
        Menu {
            title: "Game"
            MenuItem {
                text: "New Game"
                onTriggered: chessGame.resetGame()
            }
            MenuSeparator {}
            MenuItem {
                text: "Exit"
                onTriggered: Qt.quit()
            }
        }

        Menu {
            title: "Help"
            MenuItem {
                text: "About"
                onTriggered: aboutDialog.open()
            }
        }
    }

    // About dialog
    Dialog {
        id: aboutDialog
        title: "About Qt Chess"
        anchors.centerIn: parent
        width: 300
        height: 200

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20

            Text {
                text: "♔ Qt Chess Game ♛"
                font.pixelSize: 18
                font.bold: true
                Layout.alignment: Qt.AlignHCenter
            }

            Text {
                text: "A chess game built with Qt/QML and Python"
                font.pixelSize: 12
                Layout.alignment: Qt.AlignHCenter
                wrapMode: Text.WordWrap
            }

            Text {
                text: "Features:\n• Full chess rules\n• Drag and drop\n• Move validation\n• Game status tracking"
                font.pixelSize: 10
                Layout.alignment: Qt.AlignHCenter
                Layout.fillHeight: true
            }
        }

        standardButtons: Dialog.Ok
    }
}