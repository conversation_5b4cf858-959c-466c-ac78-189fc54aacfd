#!/usr/bin/env python3
"""
Tic-Tac-Toe Game with Q-Learning Neural Network AI
Features a neural network trained with Q-learning for intelligent gameplay
"""

import sys
import numpy as np
import random
import pickle
import os
from collections import deque
from enum import Enum
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEng<PERSON>, qmlRegisterType

# Try to import PyTorch, fallback to simple neural network if not available
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    print("PyTorch not available, using simple neural network implementation")


class Player(Enum):
    EMPTY = 0
    HUMAN = 1  # X
    AI = 2     # O


class GameState(Enum):
    PLAYING = 0
    HUMAN_WIN = 1
    AI_WIN = 2
    DRAW = 3


class SimpleNeuralNetwork:
    """Simple neural network implementation without PyTorch"""
    def __init__(self, input_size=9, hidden_size=128, output_size=9):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        
        # Initialize weights randomly
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, hidden_size) * 0.1
        self.b2 = np.zeros((1, hidden_size))
        self.W3 = np.random.randn(hidden_size, output_size) * 0.1
        self.b3 = np.zeros((1, output_size))
        
        self.learning_rate = 0.001
    
    def relu(self, x):
        return np.maximum(0, x)
    
    def forward(self, x):
        self.z1 = np.dot(x, self.W1) + self.b1
        self.a1 = self.relu(self.z1)
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.relu(self.z2)
        self.z3 = np.dot(self.a2, self.W3) + self.b3
        return self.z3
    
    def predict(self, x):
        return self.forward(x.reshape(1, -1))[0]


class DQNNetwork(nn.Module):
    """Deep Q-Network using PyTorch"""
    def __init__(self, input_size=9, hidden_size=128, output_size=9):
        super(DQNNetwork, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        return x


class QLearningAgent:
    """Q-Learning agent for Tic-Tac-Toe"""
    def __init__(self, learning_rate=0.001, epsilon=0.1, epsilon_decay=0.995, 
                 epsilon_min=0.01, gamma=0.95, memory_size=10000):
        self.learning_rate = learning_rate
        self.epsilon = epsilon  # Exploration rate
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.gamma = gamma  # Discount factor
        self.memory = deque(maxlen=memory_size)
        self.training_mode = True
        
        # Initialize neural network
        if PYTORCH_AVAILABLE:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.q_network = DQNNetwork().to(self.device)
            self.target_network = DQNNetwork().to(self.device)
            self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
            self.update_target_network()
        else:
            self.q_network = SimpleNeuralNetwork()
        
        # Load pre-trained model if available
        self.load_model()
    
    def update_target_network(self):
        """Update target network with current network weights"""
        if PYTORCH_AVAILABLE:
            self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def get_state_representation(self, board):
        """Convert board to neural network input"""
        # Convert Player enum to numerical values
        state = np.zeros(9)
        for i, cell in enumerate(board):
            if cell == Player.HUMAN:
                state[i] = -1  # Human = -1
            elif cell == Player.AI:
                state[i] = 1   # AI = 1
            # Empty = 0
        return state
    
    def get_valid_actions(self, board):
        """Get list of valid moves (empty cells)"""
        return [i for i, cell in enumerate(board) if cell == Player.EMPTY]
    
    def choose_action(self, board):
        """Choose action using epsilon-greedy policy"""
        valid_actions = self.get_valid_actions(board)
        if not valid_actions:
            return -1
        
        # Exploration vs exploitation
        if self.training_mode and random.random() < self.epsilon:
            return random.choice(valid_actions)
        
        # Get Q-values for current state
        state = self.get_state_representation(board)
        
        if PYTORCH_AVAILABLE:
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
                q_values = self.q_network(state_tensor).cpu().numpy()[0]
        else:
            q_values = self.q_network.predict(state)
        
        # Mask invalid actions
        masked_q_values = np.full(9, -np.inf)
        for action in valid_actions:
            masked_q_values[action] = q_values[action]
        
        return np.argmax(masked_q_values)
    
    def train(self, batch_size=32):
        """Train the neural network on a batch of experiences"""
        if len(self.memory) < batch_size:
            return
        
        batch = random.sample(self.memory, batch_size)
        
        if PYTORCH_AVAILABLE:
            self._train_pytorch(batch)
        else:
            self._train_simple(batch)
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
    
    def _train_pytorch(self, batch):
        """Training with PyTorch"""
        states = torch.FloatTensor([e[0] for e in batch]).to(self.device)
        actions = torch.LongTensor([e[1] for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e[2] for e in batch]).to(self.device)
        next_states = torch.FloatTensor([e[3] for e in batch]).to(self.device)
        dones = torch.BoolTensor([e[4] for e in batch]).to(self.device)
        
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
    
    def _train_simple(self, batch):
        """Training with simple neural network"""
        # Simplified training for the basic implementation
        for state, action, reward, next_state, done in batch:
            target = reward
            if not done:
                next_q_values = self.q_network.predict(next_state)
                target = reward + self.gamma * np.max(next_q_values)
            
            current_q_values = self.q_network.predict(state)
            current_q_values[action] = target
            
            # Simple gradient update (very basic implementation)
            # In a real implementation, you'd do proper backpropagation
            pass
    
    def save_model(self, filepath="tictactoe_qlearning_model.pkl"):
        """Save the trained model"""
        try:
            if PYTORCH_AVAILABLE:
                torch.save({
                    'q_network_state_dict': self.q_network.state_dict(),
                    'target_network_state_dict': self.target_network.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epsilon': self.epsilon
                }, filepath)
            else:
                with open(filepath, 'wb') as f:
                    pickle.dump({
                        'W1': self.q_network.W1,
                        'b1': self.q_network.b1,
                        'W2': self.q_network.W2,
                        'b2': self.q_network.b2,
                        'W3': self.q_network.W3,
                        'b3': self.q_network.b3,
                        'epsilon': self.epsilon
                    }, f)
            print(f"Model saved to {filepath}")
        except Exception as e:
            print(f"Error saving model: {e}")
    
    def load_model(self, filepath="tictactoe_qlearning_model.pkl"):
        """Load a pre-trained model"""
        try:
            if os.path.exists(filepath):
                if PYTORCH_AVAILABLE:
                    checkpoint = torch.load(filepath, map_location=self.device)
                    self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
                    self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
                    self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                    self.epsilon = checkpoint['epsilon']
                else:
                    with open(filepath, 'rb') as f:
                        data = pickle.load(f)
                        self.q_network.W1 = data['W1']
                        self.q_network.b1 = data['b1']
                        self.q_network.W2 = data['W2']
                        self.q_network.b2 = data['b2']
                        self.q_network.W3 = data['W3']
                        self.q_network.b3 = data['b3']
                        self.epsilon = data['epsilon']
                print(f"Model loaded from {filepath}")
            else:
                print("No pre-trained model found, starting with random weights")
        except Exception as e:
            print(f"Error loading model: {e}")
    
    def set_training_mode(self, training):
        """Enable/disable training mode"""
        self.training_mode = training
        if PYTORCH_AVAILABLE:
            if training:
                self.q_network.train()
            else:
                self.q_network.eval()


if __name__ == "__main__":
    # Test the Q-learning agent
    agent = QLearningAgent()
    print("Q-Learning agent initialized successfully!")
    print(f"PyTorch available: {PYTORCH_AVAILABLE}")
    print(f"Training mode: {agent.training_mode}")
    print(f"Epsilon (exploration rate): {agent.epsilon:.3f}")
