#!/usr/bin/env python3
"""
Q-Learning Tic-Tac-Toe Game Launcher
Runs the Qt/QML Tic-Tac-Toe Game with Q-Learning Neural Network AI
"""

import sys
import os
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine, qmlRegisterType

# Import our Q-learning tic-tac-toe game logic
from tictactoe_game_qlearning import TicTacToeQLearningGame


def main():
    app = QGuiApplication(sys.argv)
    
    # Register the TicTacToeQLearningGame type with QML
    qmlRegisterType(TicTacToeQLearningGame, "TicTacToeGame", 1, 0, "TicTacToeGame")
    
    # Create QML engine
    engine = QQmlApplicationEngine()
    
    # Add the current directory to the import path
    current_dir = Path(__file__).parent.absolute()
    engine.addImportPath(str(current_dir))
    
    # Load the main QML file
    main_qml = current_dir / "TicTacToeQLearningMain.qml"
    engine.load(str(main_qml))
    
    # Check if QML loaded successfully
    if not engine.rootObjects():
        print("Failed to load QML file")
        sys.exit(-1)
    
    print("🧠 Q-Learning Tic-Tac-Toe vs AI started successfully!")
    print("\n" + "="*60)
    print("🎓 REINFORCEMENT LEARNING TIC-TAC-TOE")
    print("="*60)
    print("Instructions:")
    print("- You play as X, AI plays as O")
    print("- The AI learns through Q-learning with a neural network")
    print("- In training mode, the AI explores random moves to learn")
    print("- Watch the exploration rate decrease as it gets smarter!")
    print("- Try to beat the AI as it learns and improves!")
    print("\nFeatures:")
    print("- Deep Q-Network (DQN) for decision making")
    print("- Experience replay for stable learning")
    print("- Epsilon-greedy exploration strategy")
    print("- Real-time training progress visualization")
    print("- Model saving and loading")
    print("\n🎮 Have fun watching the AI learn!")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
